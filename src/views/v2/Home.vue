<template>
  <div class="v2-home">
    <!-- 背景 -->
    <div class="background-container">
      <!-- 底图 -->
      <div class="bg-base">
        <img
          src="@/assets/img/homev2/大*********"
          alt="底图"
          class="bg-base-image"
        />
      </div>
      <!-- 上半部分覆盖图 -->
      <div class="bg-top">
        <img
          src="@/assets/img/homev2/<EMAIL>"
          alt="上半部分"
          class="bg-top-image"
        />
      </div>
    </div>

    <!-- 页面头部 -->
    <div class="header">
      <div class="header-left">
        <div class="logo-section">
          <span class="logo-text">兴山农村智慧供水平台</span>
        </div>
      </div>
    </div>

    <!-- 按钮 -->
    <div class="action-button">
      <img src="@/assets/img/homev2/按钮@3x.png" alt="按钮" />
    </div>

    <!-- 用户名 -->
    <div class="user-name">
      <img src="@/assets/img/homev2/用户名@3x.png" alt="用户名" />
    </div>

    <!-- 用户信息文字 -->
    <div class="user-text">您好，管理员</div>

    <!-- 展开按钮 -->
    <div class="expand-button">
      <img src="@/assets/img/homev2/展开@3x.png" alt="展开" />
    </div>

    <!-- 供水监管平台 -->
    <div class="platform-section">
      <div class="platform-icon" @click="handlePlatformClick">
        <img src="@/assets/img/homev2/平台@3x.png" alt="供水监管平台" />
        <div class="platform-title">供水监管平台</div>
      </div>
    </div>

    <!-- 数据统计区域 -->
    <div class="stats-section">
      <div class="stat-item">
        <img src="@/assets/img/homev2/供水@3x.png" alt="供水总量" />
        <div class="stat-content">
          <div class="stat-number">125,890</div>
          <div class="stat-label">供水总量(m³)</div>
        </div>
      </div>
      <div class="stat-item">
        <img src="@/assets/img/homev2/管网@3x.png" alt="管网长度" />
        <div class="stat-content">
          <div class="stat-number">1256</div>
          <div class="stat-label">管网长度(km)</div>
        </div>
      </div>
      <div class="stat-item">
        <img src="@/assets/img/homev2/服务@3x.png" alt="服务人口" />
        <div class="stat-content">
          <div class="stat-number">89,657</div>
          <div class="stat-label">服务人口(人)</div>
        </div>
      </div>
      <div class="stat-item">
        <img src="@/assets/img/homev2/达标@3x.png" alt="达标率" />
        <div class="stat-content">
          <div class="stat-number">99.8</div>
          <div class="stat-label">达标率(%)</div>
        </div>
      </div>
    </div>

    <!-- 业务模块区域 -->
    <div class="business-section">
      <div class="business-row">
        <div class="business-card" @click="handleCardClick(businessCards[0])">
          <img src="@/assets/img/homev2/供水调度@3x.png" alt="供水调度" />
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[1])">
          <img src="@/assets/img/homev2/管网漏损@3x.png" alt="管网漏损" />
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[2])">
          <img src="@/assets/img/homev2/营收收费@3x.png" alt="营业收费" />
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[3])">
          <img src="@/assets/img/homev2/管网**********" alt="管网GIS" />
        </div>
        <div class="business-card" @click="handleCardClick(businessCards[4])">
          <img src="@/assets/img/homev2/水质监测@3x.png" alt="水质检测" />
        </div>
      </div>
    </div>

    <!-- 平台管理区域 -->
    <div class="platform-management">
      <div class="platform-row">
        <div class="platform-card">
          <img
            src="@/assets/img/homev2/公共管理平台@3x.png"
            alt="公共管理平台"
          />
          <div class="platform-name">公共管理平台</div>
        </div>
        <div class="platform-card">
          <img
            src="@/assets/img/homev2/物联网接入平台@3x.png"
            alt="物联网接入平台"
          />
          <div class="platform-name">物联网接入平台</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/<EMAIL>" alt="GIS服务平台" />
          <div class="platform-name">GIS服务平台</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/工单@3x.png" alt="工单接入平台" />
          <div class="platform-name">工单接入平台</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/统一报警@3x.png" alt="统一报警中心" />
          <div class="platform-name">统一报警中心</div>
        </div>
        <div class="platform-card">
          <img src="@/assets/img/homev2/视频监控@3x.png" alt="视频监控平台" />
          <div class="platform-name">视频监控平台</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "V2Home",
  data() {
    return {
      // 业务模块卡片数据
      businessCards: [
        {
          id: 1,
          title: "供水调度",
          route: { name: "WsScreen" },
        },
        {
          id: 2,
          title: "管网漏损",
          route: { name: "WlScreen" },
        },
        {
          id: 3,
          title: "营业收费",
          route: null, // 暂时不做跳转
        },
        {
          id: 4,
          title: "管网GIS",
          route: { name: "PipeNetworkIndex" },
        },
        {
          id: 5,
          title: "水质检测",
          route: { name: "WqScreen" },
        },
      ],
    };
  },
  methods: {
    // 处理业务卡片点击
    handleCardClick(card) {
      if (card && card.route) {
        this.$router.push(card.route);
      }
    },
    // 处理供水监管平台点击 - 跳转到供水一张图
    handlePlatformClick() {
      this.$router.push({ name: "InfoMap" });
    },
  },
};
</script>

<style lang="scss" scoped>
.v2-home {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  color: #fff;

  // 背景容器 - 绝对定位铺满全屏
  .background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    // 底图 - 铺满整个页面
    .bg-base {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      .bg-base-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    // 上半部分覆盖图
    .bg-top {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 520px;
      z-index: 2;

      .bg-top-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  // 页面头部 - 绝对定位
  .header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;

    .header-left {
      .logo-section {
        position: absolute;
        left: 50%;
        top: 34px;
        transform: translateX(-50%);

        .logo-text {
          width: 379px;
          height: 46px;
          font-family: PangMenZhengDao, PangMenZhengDao;
          font-weight: 400;
          font-size: 40px;
          color: #ffffff;
          line-height: 46px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          opacity: 1;
        }
      }
    }

    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        gap: 30px;

        .back-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          color: #fff;
          font-size: 14px;

          img {
            width: 16px;
            height: 16px;
          }

          &:hover {
            opacity: 0.8;
          }
        }

        .user-section {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #fff;
          font-size: 14px;

          img {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  // 按钮 - 绝对定位
  .action-button {
    position: absolute;
    top: 35px;
    left: 1521px;
    z-index: 10;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }

    img {
      width: 177px;
      height: 44px;
      opacity: 1; // 100% 不透明度
    }
  }

  // 用户名图标 - 绝对定位
  .user-name {
    position: absolute;
    top: 43px;
    left: 1718px;
    z-index: 10;

    img {
      width: 28px;
      height: 28px;
      opacity: 1; // 100% 不透明度
    }
  }

  // 用户信息文字 - 绝对定位
  .user-text {
    position: absolute;
    top: 43px;
    left: 1756px; // 用户名图标右侧 + 10px间距
    z-index: 10;
    height: 32px;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 18px;
    color: #ffffff;
    line-height: 32px;
    text-align: right;
    font-style: normal;
    text-transform: none;
    white-space: nowrap; // 防止换行
  }

  // 展开按钮 - 绝对定位
  .expand-button {
    position: absolute;
    top: 53px;
    left: 1874px;
    z-index: 10;
    cursor: pointer;
    transition: transform 0.3s ease;
    &:hover {
      transform: scale(1.1);
    }

    img {
      width: 16px;
      height: 8.5px;
      opacity: 1; // 100% 不透明度
      object-fit: contain; // 保持图片比例，适应容器
      display: block; // 确保图片正常显示
    }
  }

  // 供水监管平台 - 根据设计稿精确定位
  .platform-section {
    position: absolute;
    top: 110px;
    left: 880px;
    z-index: 5;

    .platform-icon {
      cursor: pointer;
      transition: transform 0.3s ease;
      text-align: center;
      opacity: 1; // 100% 不透明度

      &:hover {
        transform: scale(1.05);
      }

      img {
        width: 160px;
        height: 242px;
        display: block;
      }

      .platform-title {
        width: 168px;
        height: 37px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 700;
        font-size: 28px;
        color: #ffffff;
        line-height: 37px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin: 10px auto 0 auto; // 上边距10px，左右居中
      }
    }
  }

  // 数据统计区域 - 根据设计稿精确定位
  .stats-section {
    position: absolute;
    top: 420px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    width: 1620px;
    height: 100px;
    background-image: url("~@/assets/img/homev2/透明底@3x.png");
    background-size: 1620px 100px;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    justify-content: space-between;

    .stat-item {
      position: relative;
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center; // 确保整个内容垂直居中
      padding-left: 80px; // 距离卡片左边缘80px

      // 添加分割线（除了最后一个卡片）
      &:not(:last-child)::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: auto;
        height: auto;
        background-image: url("~@/assets/img/homev2/分割线@3x.png");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        // 根据实际分割线图片尺寸调整
        min-width: 2px;
        min-height: 60px;
      }

      img {
        flex-shrink: 0; // 防止图片被压缩
        display: block; // 确保图片正常显示
        object-fit: contain; // 保持图片比例
      }

      // 供水总量图标 40*50
      &:nth-child(1) img {
        width: 40px;
        height: 50px;
      }

      // 管网长度图标 50*48
      &:nth-child(2) img {
        width: 50px;
        height: 48px;
      }

      // 服务人口图标 50*36
      &:nth-child(3) img {
        width: 50px;
        height: 36px;
      }

      // 达标率图标 50*43
      &:nth-child(4) img {
        width: 50px;
        height: 43px;
      }

      .stat-content {
        margin-left: 10px; // 图标和文字之间10px间距
        display: flex;
        flex-direction: column;
        justify-content: center; // 文字内容垂直居中

        .stat-number {
          // 移除固定宽度，让数字长度自适应
          min-width: 58px;
          height: 32px;
          font-family: DIN, DIN;
          font-weight: 700;
          font-size: 30px;
          color: #ffffff;
          line-height: 32px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-bottom: 8px;
          white-space: nowrap; // 防止换行
        }

        .stat-label {
          // 移除固定宽度，让文字长度自适应
          min-width: 91px;
          height: 21px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          white-space: nowrap; // 防止换行
        }
      }
    }
  }

  // 业务模块区域 - 绝对定位
  .business-section {
    position: absolute;
    top: 570px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    // 5个卡片320px + 4个间距(37+38+37+38) = 1600 + 150 = 1750px
    width: 1750px;

    .business-row {
      display: flex;

      .business-card {
        width: 320px;
        height: 160px;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);
        }

        // 设置具体的间距：37px, 38px, 37px, 38px
        &:nth-child(1) {
          margin-right: 37px;
        }

        &:nth-child(2) {
          margin-right: 38px;
        }

        &:nth-child(3) {
          margin-right: 37px;
        }

        &:nth-child(4) {
          margin-right: 38px;
        }

        // 最后一个卡片不需要右边距
        &:nth-child(5) {
          margin-right: 0;
        }

        img {
          width: 320px;
          height: 160px;
          object-fit: cover; // 确保图片填满卡片区域
        }
      }
    }
  }

  // 平台管理区域 - 绝对定位在页面底部
  .platform-management {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    // 6个卡片170px + 5个间距120px = 1020 + 600 = 1620px
    width: 1620px;

    .platform-row {
      display: flex;

      .platform-card {
        width: 170px;
        height: 190px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: transform 0.3s ease;

        // 设置120px间距（除了最后一个卡片）
        &:not(:last-child) {
          margin-right: 120px;
        }

        &:hover {
          transform: scale(1.05);
        }

        img {
          width: 170px;
          height: 160px; // 调整图片高度，为文字留出空间
          object-fit: contain;
        }

        .platform-name {
          min-width: 120px; // 最小宽度，支持自适应
          height: 26px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 700;
          font-size: 20px;
          color: #1377d0;
          line-height: 26px;
          text-align: center;
          font-style: normal;
          text-transform: none;
          margin-top: 5px; // 距离切图5px
          white-space: nowrap; // 防止换行，支持宽度自适应
        }
      }
    }
  }
}
</style>
